import { useState, useEffect } from 'react';
import { ThemeProvider } from 'styled-components';
import { useThemeStore } from './store';
import { GlobalStyles } from './styles/GlobalStyles';
import Desktop from './features/desktop/Desktop';
import WindowManager from './features/window-manager/WindowManager';
import Dock from './features/dock/components/Dock';
import VirtualKeyboard from './features/virtual-keyboard/VirtualKeyboard';
import PerformanceMonitor from './components/performance/PerformanceMonitor';
import 'remixicon/fonts/remixicon.css';

// Default theme fallback
const defaultTheme = {
  colors: {
    primary: '#007AFF',
    secondary: '#5856D6',
    background: '#FFFFFF',
    surface: '#F2F2F7',
    text: '#000000',
    textSecondary: '#6D6D70',
    border: '#C6C6C8',
    accent: '#FF9500',
    success: '#34C759',
    warning: '#FF9500',
    error: '#FF3B30',
    glass: 'rgba(255, 255, 255, 0.8)',
    glassStrong: 'rgba(255, 255, 255, 0.95)'
  },
  fonts: {
    system: '-apple-system, BlinkMacSystemFont, "SF Pro Display", "Segoe UI", Roboto, Helvetica, Arial, sans-serif',
    mono: '"SF Mono", Monaco, "Cascadia Code", "Roboto Mono", Consolas, "Courier New", monospace'
  },
  spacing: {
    xs: '0.25rem',
    sm: '0.5rem',
    md: '1rem',
    lg: '1.5rem',
    xl: '2rem'
  },
  borderRadius: {
    sm: '0.375rem',
    md: '0.5rem',
    lg: '0.75rem',
    xl: '1rem'
  },
  shadows: {
    sm: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
    md: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
    lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
    xl: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)'
  }
};

/**
 * Main App component that provides the macOS/iOS-style operating system
 * Uses ThemeProvider to provide theme context to all components
 */
const App = () => {
  const { getCurrentTheme, setSystemPrefersDark } = useThemeStore();
  const storeTheme = getCurrentTheme();
  const theme = storeTheme || defaultTheme;
  const [keyboardVisible, setKeyboardVisible] = useState(false);

  // Initialize theme based on system preference
  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    setSystemPrefersDark(mediaQuery.matches);

    const handleChange = (e) => setSystemPrefersDark(e.matches);
    mediaQuery.addEventListener('change', handleChange);

    return () => mediaQuery.removeEventListener('change', handleChange);
  }, [setSystemPrefersDark]);

  // Enable performance monitoring in development
  const isDevelopment = import.meta.env.DEV;

  return (
    <ThemeProvider theme={theme}>
      <GlobalStyles />
      <Desktop />
      <WindowManager />
      <Dock />
      <VirtualKeyboard
        visible={keyboardVisible}
        onClose={() => setKeyboardVisible(false)}
      />
      <PerformanceMonitor enabled={isDevelopment} />
    </ThemeProvider>
  );
};



export default App;