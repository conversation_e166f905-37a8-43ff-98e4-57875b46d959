import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';

/**
 * Window state shape:
 * {
 *   id: string,
 *   title: string,
 *   content: string,
 *   minimized: boolean,
 *   maximized: boolean,
 *   position: { x: number, y: number },
 *   size: { width: number, height: number },
 *   zIndex: number,
 *   appId: string,
 *   prevPosition?: { x: number, y: number },
 *   prevSize?: { width: number, height: number }
 * }
 */

/**
 * Window store shape:
 * {
 *   windows: WindowState[],
 *   activeWindow: string | null,
 *   maxZIndex: number,
 *   isDragging: boolean,
 *   draggedWindow: string | null,
 *   // Actions
 *   openWindow: (appId, title, content?) => void,
 *   closeWindow: (windowId) => void,
 *   minimizeWindow: (windowId) => void,
 *   maximizeWindow: (windowId) => void,
 *   restoreWindow: (windowId) => void,
 *   bringToFront: (windowId) => void,
 *   updateWindowPosition: (windowId, position) => void,
 *   updateWindowSize: (windowId, size) => void,
 *   setActiveWindow: (windowId) => void,
 *   setDragging: (isDragging, windowId?) => void,
 *   getWindowById: (windowId) => WindowState | undefined
 * }
 */

const useWindowStore = create(
  subscribeWithSelector((set, get) => ({
    windows: [],
    activeWindow: null,
    maxZIndex: 1,
    isDragging: false,
    draggedWindow: null,

    openWindow: (appId, title, content = `This is the ${title} window content.`) => {
      const { windows, maxZIndex } = get();

      // Check if window already exists
      const existingWindow = windows.find(w => w.appId === appId);
      if (existingWindow) {
        if (existingWindow.minimized) {
          get().restoreWindow(existingWindow.id);
        }
        get().bringToFront(existingWindow.id);
        return;
      }

      const newWindow = {
        id: `window-${appId}-${Date.now()}`,
        title,
        content,
        minimized: false,
        maximized: false,
        position: {
          x: 150 + (windows.length * 30),
          y: 150 + (windows.length * 20)
        },
        size: { width: 600, height: 400 },
        zIndex: maxZIndex + 1,
        appId
      };

      set(state => ({
        windows: [...state.windows, newWindow],
        activeWindow: newWindow.id,
        maxZIndex: state.maxZIndex + 1
      }));
    },

    closeWindow: (windowId) => {
      set(state => ({
        windows: state.windows.filter(w => w.id !== windowId),
        activeWindow: state.activeWindow === windowId ? null : state.activeWindow
      }));
    },

    minimizeWindow: (windowId) => {
      set(state => ({
        windows: state.windows.map(w =>
          w.id === windowId ? { ...w, minimized: true } : w
        ),
        activeWindow: state.activeWindow === windowId ? null : state.activeWindow
      }));
    },

    maximizeWindow: (windowId) => {
      set(state => ({
        windows: state.windows.map(w => {
          if (w.id === windowId) {
            if (w.maximized) {
              // Restore to previous size/position
              return {
                ...w,
                maximized: false,
                position: w.prevPosition || { x: 150, y: 150 },
                size: w.prevSize || { width: 600, height: 400 }
              };
            } else {
              // Maximize
              return {
                ...w,
                maximized: true,
                prevPosition: w.position,
                prevSize: w.size,
                position: { x: 0, y: 32 },
                size: { width: window.innerWidth, height: window.innerHeight - 32 - 80 }
              };
            }
          }
          return w;
        })
      }));
    },

    restoreWindow: (windowId) => {
      set(state => ({
        windows: state.windows.map(w =>
          w.id === windowId ? { ...w, minimized: false } : w
        )
      }));
      get().bringToFront(windowId);
    },

    bringToFront: (windowId) => {
      set(state => ({
        windows: state.windows.map(w =>
          w.id === windowId ? { ...w, zIndex: state.maxZIndex + 1 } : w
        ),
        activeWindow: windowId,
        maxZIndex: state.maxZIndex + 1
      }));
    },

    updateWindowPosition: (windowId, position) => {
      set(state => ({
        windows: state.windows.map(w =>
          w.id === windowId ? { ...w, position } : w
        )
      }));
    },

    updateWindowSize: (windowId, size) => {
      set(state => ({
        windows: state.windows.map(w =>
          w.id === windowId ? { ...w, size } : w
        )
      }));
    },

    setActiveWindow: (windowId) => {
      set({ activeWindow: windowId });
    },

    setDragging: (isDragging, windowId = null) => {
      set({ isDragging, draggedWindow: windowId });
    },

    getWindowById: (windowId) => {
      return get().windows.find(w => w.id === windowId);
    }
  }))
);

export default useWindowStore;
