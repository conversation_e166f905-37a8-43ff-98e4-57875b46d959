import React, { memo, useEffect } from 'react';
import styled from 'styled-components';
import { useDesktopStore, useThemeStore } from '../../store';
import IconGrid from './components/IconGrid';
import MenuBar from '../menu-bar/MenuBar';

/**
 * Styled components for the desktop
 */
const DesktopContainer = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: url(${props => props.wallpaper});
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  background-attachment: fixed;
  overflow: hidden;
  user-select: none;

  /* Fallback gradient if wallpaper fails to load */
  background-color: ${props => props.theme?.colors?.background || '#000000'};

  /* Add subtle overlay for better text readability */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.1);
    pointer-events: none;
    z-index: 1;
  }
`;

const DesktopContent = styled.div`
  position: relative;
  width: 100%;
  height: 100%;
  z-index: 2;
`;

/**
 * Desktop component that provides the main desktop environment
 * Includes wallpaper, icon grid, and menu bar
 */
const Desktop = memo(() => {
  const { wallpaper, deselectAll } = useDesktopStore();
  const { setSystemPrefersDark } = useThemeStore();

  // Handle system theme preference detection
  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    
    const handleChange = (e) => {
      setSystemPrefersDark(e.matches);
    };

    // Set initial value
    setSystemPrefersDark(mediaQuery.matches);
    
    // Listen for changes
    mediaQuery.addEventListener('change', handleChange);
    
    return () => {
      mediaQuery.removeEventListener('change', handleChange);
    };
  }, [setSystemPrefersDark]);

  // Handle desktop click to deselect all icons
  const handleDesktopClick = (e) => {
    if (e.target === e.currentTarget) {
      deselectAll();
    }
  };

  // Handle keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e) => {
      // Escape key to deselect all
      if (e.key === 'Escape') {
        deselectAll();
      }
      
      // Cmd/Ctrl + A to select all (future feature)
      if ((e.metaKey || e.ctrlKey) && e.key === 'a') {
        e.preventDefault();
        // TODO: Implement select all functionality
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [deselectAll]);

  return (
    <DesktopContainer 
      wallpaper={wallpaper}
      onClick={handleDesktopClick}
    >
      <DesktopContent>
        <MenuBar />
        <IconGrid />
      </DesktopContent>
    </DesktopContainer>
  );
});

Desktop.displayName = 'Desktop';

export default Desktop;
