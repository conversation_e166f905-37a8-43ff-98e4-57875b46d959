import { useState, useCallback, useEffect, useMemo, memo } from 'react';
import styled from 'styled-components';
import {
  RiDeleteBack2Line,
  RiArrowUpSLine,
  RiCloseLine
} from 'react-icons/ri';
import { glassEffect } from '../../styles/components';

/**
 * Styled components for the virtual keyboard
 */
const KeyboardContainer = styled.div`
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  ${glassEffect}
  backdrop-filter: blur(40px);
  -webkit-backdrop-filter: blur(40px);
  border-top: 1px solid rgba(255, 255, 255, 0.2);
  padding: ${props => props.theme.spacing.md};
  z-index: 2000;
  transform: translateY(${props => props.visible ? '0' : '100%'});
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  
  @media (min-width: 768px) {
    display: none; /* Hide on desktop by default */
  }
`;

const KeyboardHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${props => props.theme.spacing.sm};
  padding: 0 ${props => props.theme.spacing.sm};
`;

const KeyboardTitle = styled.span`
  color: white;
  font-size: 0.875rem;
  font-weight: 500;
`;

const CloseButton = styled.button`
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  padding: ${props => props.theme.spacing.xs};
  border-radius: ${props => props.theme.borderRadius.sm};
  transition: background-color 0.2s ease;

  &:hover {
    background: rgba(255, 255, 255, 0.1);
  }
`;

const KeyboardGrid = styled.div`
  display: grid;
  gap: ${props => props.theme.spacing.xs};
  grid-template-columns: repeat(10, 1fr);
  margin-bottom: ${props => props.theme.spacing.sm};
`;

const KeyboardRow = styled.div`
  display: grid;
  gap: ${props => props.theme.spacing.xs};
  margin-bottom: ${props => props.theme.spacing.xs};
  
  &.row-1 {
    grid-template-columns: repeat(10, 1fr);
  }
  
  &.row-2 {
    grid-template-columns: repeat(9, 1fr);
  }
  
  &.row-3 {
    grid-template-columns: 1.5fr repeat(7, 1fr) 1.5fr;
  }
  
  &.row-4 {
    grid-template-columns: 1fr 1fr 4fr 1fr 1fr;
  }
`;

const Key = styled.button`
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  color: white;
  font-size: 1rem;
  font-weight: 500;
  padding: 8px;
  min-height: 2.5rem;
  cursor: pointer;
  transition: background-color 0.15s ease, transform 0.1s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  user-select: none;
  will-change: transform, background-color;

  &:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-1px);
  }

  &:active {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(0);
  }

  ${props => props.special && `
    background: rgba(255, 255, 255, 0.15);
    font-size: 0.875rem;
  `}

  ${props => props.wide && `
    grid-column: span 2;
  `}

  ${props => props.space && `
    grid-column: span 4;
  `}

  ${props => props.shift && props.active && `
    background: rgba(59, 130, 246, 0.4);
    border-color: rgb(59, 130, 246);
  `}
`;

const PredictiveText = styled.div`
  display: flex;
  gap: ${props => props.theme.spacing.sm};
  margin-bottom: ${props => props.theme.spacing.sm};
  padding: 0 ${props => props.theme.spacing.sm};
  overflow-x: auto;
`;

const Suggestion = styled.button`
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: ${props => props.theme.borderRadius.md};
  color: white;
  font-size: 0.875rem;
  padding: ${props => props.theme.spacing.xs} ${props => props.theme.spacing.sm};
  cursor: pointer;
  transition: background-color 0.2s ease;
  white-space: nowrap;

  &:hover {
    background: rgba(255, 255, 255, 0.2);
  }
`;

/**
 * Keyboard layouts - moved outside component for performance
 */
const QWERTY_LAYOUT = [
  ['1', '2', '3', '4', '5', '6', '7', '8', '9', '0'],
  ['q', 'w', 'e', 'r', 't', 'y', 'u', 'i', 'o', 'p'],
  ['a', 's', 'd', 'f', 'g', 'h', 'j', 'k', 'l'],
  ['shift', 'z', 'x', 'c', 'v', 'b', 'n', 'm', 'backspace']
];

const SHIFT_LAYOUT = [
  ['!', '@', '#', '$', '%', '^', '&', '*', '(', ')'],
  ['Q', 'W', 'E', 'R', 'T', 'Y', 'U', 'I', 'O', 'P'],
  ['A', 'S', 'D', 'F', 'G', 'H', 'J', 'K', 'L'],
  ['shift', 'Z', 'X', 'C', 'V', 'B', 'N', 'M', 'backspace']
];

// Common words for suggestions - moved outside for performance
const COMMON_WORDS = [
  'the', 'and', 'for', 'are', 'but', 'not', 'you', 'all', 'can', 'had',
  'her', 'was', 'one', 'our', 'out', 'day', 'get', 'has', 'him', 'his',
  'how', 'its', 'may', 'new', 'now', 'old', 'see', 'two', 'way', 'who',
  'boy', 'did', 'man', 'end', 'few', 'got', 'let', 'put', 'say', 'she',
  'too', 'use', 'about', 'after', 'again', 'before', 'being', 'between',
  'both', 'during', 'each', 'first', 'from', 'great', 'here', 'into',
  'just', 'know', 'last', 'like', 'long', 'make', 'many', 'most', 'much',
  'never', 'only', 'other', 'over', 'right', 'same', 'should', 'since',
  'some', 'still', 'such', 'take', 'than', 'that', 'their', 'them',
  'there', 'these', 'they', 'this', 'those', 'through', 'time', 'under',
  'until', 'very', 'well', 'what', 'when', 'where', 'which', 'while',
  'will', 'with', 'work', 'would', 'year', 'your'
];

// Memoized Key component for better performance
const KeyComponent = memo(({ keyValue, isShiftActive, onKeyPress }) => {
  let keyContent = keyValue;
  let keyProps = {};

  switch (keyValue) {
    case 'shift':
      keyContent = <RiArrowUpSLine size={16} />;
      keyProps = { special: true, shift: true, active: isShiftActive };
      break;
    case 'backspace':
      keyContent = <RiDeleteBack2Line size={16} />;
      keyProps = { special: true };
      break;
    case 'space':
      keyContent = 'Space';
      keyProps = { special: true, space: true };
      break;
    case 'enter':
      keyContent = '↵';
      keyProps = { special: true };
      break;
    default:
      keyContent = keyValue;
      break;
  }

  const handleClick = useCallback(() => onKeyPress(keyValue), [keyValue, onKeyPress]);

  return (
    <Key
      onClick={handleClick}
      {...keyProps}
    >
      {keyContent}
    </Key>
  );
});

KeyComponent.displayName = 'KeyComponent';

// Optimized suggestions generator
const generateSuggestions = (text) => {
  if (!text || text.length < 2) return [];

  const lowerText = text.toLowerCase();
  return COMMON_WORDS
    .filter(word => word.startsWith(lowerText))
    .slice(0, 3);
};

/**
 * Virtual Keyboard component for touch devices
 * Provides on-screen keyboard with predictive text
 */
const VirtualKeyboard = ({ visible, onClose, onKeyPress, targetInput }) => {
  const [isShiftActive, setIsShiftActive] = useState(false);
  const [currentText, setCurrentText] = useState('');
  const [suggestions, setSuggestions] = useState([]);

  // Memoize layout to prevent recreation
  const layout = useMemo(() =>
    isShiftActive ? SHIFT_LAYOUT : QWERTY_LAYOUT,
    [isShiftActive]
  );

  // Optimized key press handler
  const handleKeyPress = useCallback((key) => {
    let processedKey = key;

    switch (key) {
      case 'shift':
        setIsShiftActive(prev => !prev);
        return;

      case 'backspace':
        setCurrentText(prev => {
          const newText = prev.slice(0, -1);
          const currentWord = newText.split(' ').pop() || '';
          setSuggestions(generateSuggestions(currentWord));
          return newText;
        });
        processedKey = 'Backspace';
        break;

      case 'space':
        setCurrentText(prev => prev + ' ');
        setSuggestions([]);
        processedKey = ' ';
        break;

      case 'enter':
        processedKey = 'Enter';
        break;

      default:
        setCurrentText(prev => {
          const newText = prev + key;
          const currentWord = newText.split(' ').pop() || '';
          setSuggestions(generateSuggestions(currentWord));
          return newText;
        });

        // Auto-disable shift after typing a character
        if (isShiftActive && key.length === 1) {
          setIsShiftActive(false);
        }
        break;
    }

    // Call the onKeyPress callback if provided
    onKeyPress?.(processedKey);

    // Simulate keyboard event for the target input
    if (targetInput?.current) {
      const event = new KeyboardEvent('keydown', {
        key: processedKey,
        code: `Key${key.toUpperCase()}`,
        bubbles: true
      });
      targetInput.current.dispatchEvent(event);
    }
  }, [isShiftActive, onKeyPress, targetInput]);

  // Optimized suggestion selection handler
  const handleSuggestionSelect = useCallback((suggestion) => {
    const words = currentText.split(' ');
    words[words.length - 1] = suggestion;
    const newText = words.join(' ') + ' ';

    setCurrentText(newText);
    setSuggestions([]);

    // Send the suggestion as a series of key presses
    if (onKeyPress) {
      suggestion.split('').forEach(char => onKeyPress(char));
      onKeyPress(' ');
    }
  }, [currentText, onKeyPress]);

  // Handle escape key to close keyboard
  useEffect(() => {
    const handleEscape = (e) => {
      if (e.key === 'Escape' && visible) {
        onClose();
      }
    };

    window.addEventListener('keydown', handleEscape);
    return () => window.removeEventListener('keydown', handleEscape);
  }, [visible, onClose]);

  if (!visible) return null;

  return (
    <KeyboardContainer visible={visible}>
      <KeyboardHeader>
        <KeyboardTitle>Virtual Keyboard</KeyboardTitle>
        <CloseButton onClick={onClose}>
          <RiCloseLine size={20} />
        </CloseButton>
      </KeyboardHeader>

      {suggestions.length > 0 && (
        <PredictiveText>
          {suggestions.map((suggestion, index) => (
            <Suggestion
              key={index}
              onClick={() => handleSuggestionSelect(suggestion)}
            >
              {suggestion}
            </Suggestion>
          ))}
        </PredictiveText>
      )}

      {layout.map((row, rowIndex) => (
        <KeyboardRow key={rowIndex} className={`row-${rowIndex + 1}`}>
          {row.map((key, keyIndex) => (
            <KeyComponent
              key={`${key}-${keyIndex}`}
              keyValue={key}
              isShiftActive={isShiftActive}
              onKeyPress={handleKeyPress}
            />
          ))}
        </KeyboardRow>
      ))}

      <KeyboardRow className="row-4">
        <Key special onClick={() => handleKeyPress('123')}>123</Key>
        <Key special onClick={() => handleKeyPress('emoji')}>😀</Key>
        <Key space onClick={() => handleKeyPress('space')}>
          Space
        </Key>
        <Key special onClick={() => handleKeyPress('.')}>.</Key>
        <Key special onClick={() => handleKeyPress('enter')}>↵</Key>
      </KeyboardRow>
    </KeyboardContainer>
  );
};

export default memo(VirtualKeyboard);
